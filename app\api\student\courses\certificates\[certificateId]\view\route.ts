import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { certificateId: string } }
) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }

    const { certificateId } = params

    // Find the certificate
    const certificate = await prisma.courseCertificate.findFirst({
      where: {
        certificateId,
        userId: session.user.id
      },
      include: {
        course: {
          include: {
            instructor: true
          }
        }
      }
    })

    if (!certificate) {
      return new NextResponse('Certificate not found', { status: 404 })
    }

    // Generate HTML certificate view
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate of Completion - ${certificate.course.title}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Georgia', 'Times New Roman', serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .certificate {
            background: white;
            width: 800px;
            max-width: 100%;
            padding: 60px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .certificate::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
        }
        
        .certificate-header {
            margin-bottom: 40px;
        }
        
        .certificate-title {
            font-size: 48px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .certificate-subtitle {
            font-size: 24px;
            color: #667eea;
            font-weight: 300;
        }
        
        .certificate-body {
            margin: 40px 0;
            line-height: 1.8;
        }
        
        .certificate-text {
            font-size: 20px;
            color: #4a5568;
            margin-bottom: 30px;
        }
        
        .student-name {
            font-size: 36px;
            font-weight: bold;
            color: #2d3748;
            margin: 20px 0;
            padding: 15px 30px;
            background: linear-gradient(135deg, #f7fafc, #edf2f7);
            border-radius: 10px;
            border-left: 5px solid #667eea;
        }
        
        .course-title {
            font-size: 24px;
            color: #667eea;
            font-weight: bold;
            margin: 20px 0;
            font-style: italic;
        }
        
        .certificate-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 40px 0;
            padding: 30px;
            background: #f7fafc;
            border-radius: 15px;
        }
        
        .detail-item {
            text-align: center;
        }
        
        .detail-label {
            font-size: 14px;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 5px;
        }
        
        .detail-value {
            font-size: 18px;
            color: #2d3748;
            font-weight: bold;
        }
        
        .certificate-footer {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid #e2e8f0;
        }
        
        .certificate-id {
            font-size: 14px;
            color: #718096;
            margin-bottom: 10px;
        }
        
        .issued-date {
            font-size: 16px;
            color: #4a5568;
        }
        
        .actions {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
        }
        
        .btn-secondary:hover {
            background: #667eea;
            color: white;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .certificate {
                box-shadow: none;
                border-radius: 0;
                max-width: none;
                width: 100%;
            }
            
            .actions {
                display: none;
            }
        }
        
        @media (max-width: 768px) {
            .certificate {
                padding: 30px 20px;
            }
            
            .certificate-title {
                font-size: 32px;
            }
            
            .certificate-subtitle {
                font-size: 18px;
            }
            
            .student-name {
                font-size: 24px;
            }
            
            .course-title {
                font-size: 18px;
            }
            
            .certificate-details {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="certificate">
        <div class="certificate-header">
            <h1 class="certificate-title">Certificate</h1>
            <p class="certificate-subtitle">of Completion</p>
        </div>
        
        <div class="certificate-body">
            <p class="certificate-text">This is to certify that</p>
            
            <div class="student-name">${session.user.name || 'Student'}</div>
            
            <p class="certificate-text">has successfully completed the course</p>
            
            <div class="course-title">"${certificate.course.title}"</div>
        </div>
        
        <div class="certificate-details">
            <div class="detail-item">
                <div class="detail-label">Completion Date</div>
                <div class="detail-value">${certificate.completionDate.toLocaleDateString('en-US', { 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Instructor</div>
                <div class="detail-value">${certificate.course.instructor?.name || 'Instructor'}</div>
            </div>
        </div>
        
        <div class="certificate-footer">
            <div class="certificate-id">Certificate ID: ${certificate.certificateId}</div>
            <div class="issued-date">Issued: ${certificate.issuedAt.toLocaleDateString('en-US', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}</div>
        </div>
        
        <div class="actions">
            <button class="btn btn-primary" onclick="window.print()">
                🖨️ Print Certificate
            </button>
            <a href="/student/courses" class="btn btn-secondary">
                📚 Back to Courses
            </a>
        </div>
    </div>
</body>
</html>
    `

    return new NextResponse(html, {
      headers: {
        'Content-Type': 'text/html',
      },
    })

  } catch (error) {
    console.error('Error displaying certificate:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}
