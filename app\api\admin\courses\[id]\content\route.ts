import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/admin/courses/[id]/content - Get all course content for roadmap configuration
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const courseId = params?.id as string

      if (!courseId) {
        return APIResponse.badRequest('Course ID is required')
      }

      // Verify course exists and user has access
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { id: true, title: true }
      })

      if (!course) {
        return APIResponse.notFound('Course not found')
      }

      // Fetch all course content (lessons, quizzes, assignments)
      const courseContent = await prisma.course.findUnique({
        where: { id: courseId },
        include: {
          sections: {
            include: {
              chapters: {
                include: {
                  lessons: {
                    select: {
                      id: true,
                      title: true,
                      type: true,
                      order: true
                    },
                    orderBy: { order: 'asc' }
                  }
                },
                orderBy: { order: 'asc' }
              }
            },
            orderBy: { order: 'asc' }
          },
          quizzes: {
            select: {
              id: true,
              title: true,
              order: true
            },
            orderBy: { order: 'asc' }
          }
        }
      })

      if (!courseContent) {
        return APIResponse.notFound('Course content not found')
      }

      // Transform the data into a flat structure for easier use
      const content: Array<{
        id: string
        title: string
        type: 'LESSON' | 'QUIZ' | 'ASSIGNMENT' | 'DISCUSSION'
        sectionTitle?: string
        chapterTitle?: string
        order: number
      }> = []

      // Add lessons from sections/chapters
      courseContent.sections.forEach(section => {
        section.chapters.forEach(chapter => {
          chapter.lessons.forEach(lesson => {
            content.push({
              id: lesson.id,
              title: lesson.title,
              type: lesson.type as 'LESSON' | 'QUIZ' | 'ASSIGNMENT' | 'DISCUSSION',
              sectionTitle: section.title,
              chapterTitle: chapter.title,
              order: lesson.order
            })
          })
        })
      })

      // Add standalone course quizzes
      courseContent.quizzes.forEach(quiz => {
        content.push({
          id: quiz.id,
          title: quiz.title,
          type: 'QUIZ',
          order: quiz.order || 0
        })
      })

      // Sort by section order, chapter order, then lesson order
      content.sort((a, b) => a.order - b.order)

      return APIResponse.success({
        content,
        totalItems: content.length
      }, 'Course content retrieved successfully')

    } catch (error) {
      console.error('Error fetching course content:', error)
      return APIResponse.internalServerError('Failed to fetch course content')
    }
  }
)
